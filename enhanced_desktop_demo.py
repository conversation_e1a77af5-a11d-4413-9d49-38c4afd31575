"""
Enhanced Desktop Application Demo
Demonstrates the new strategy criteria display functionality

This script launches the enhanced desktop application with the new
strategy criteria analysis features including:
- Visual criteria checklist with color-coded indicators
- Real-time criteria evaluation
- Detailed strategy justification
- Educational tooltips and explanations

Usage: python enhanced_desktop_demo.py
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def check_dependencies():
    """Check if all required modules are available"""
    required_modules = [
        'strategy_criteria_display',
        'intelligent_strategy_engine', 
        'market_analysis_engine',
        'desktop_app'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"Missing required modules: {', '.join(missing_modules)}")
        print("Please ensure all trading system modules are available.")
        return False
    
    return True

def create_demo_data():
    """Create demo data for testing the criteria display"""
    from market_analysis_engine import MarketFactors, StockSpecificFactors, MarketRegime, VolatilityRegime, SentimentLevel
    from strategy_decision_tree import StrategyRecommendation, StrategyConfidence
    from daily_outline import StrategyType
    from datetime import datetime
    
    # Demo market factors
    demo_market_factors = MarketFactors(
        vix_level=22.5,
        market_regime=MarketRegime.BULL_MARKET,
        volatility_regime=VolatilityRegime.HIGH_VOLATILITY,
        trend_strength=0.75,
        regime_confidence=0.85,
        bullish_factor_score=0.7,
        bearish_factor_score=0.3,
        volatility_factor_score=0.8,
        uncertainty_factor_score=0.4,
        sentiment_level=SentimentLevel.BULLISH,
        put_call_ratio=0.85,
        market_breadth_score=0.65,
        fear_greed_index=65,
        social_sentiment_score=0.6,
        spy_technical_score=0.8,
        sector_rotation_score=0.7,
        earnings_calendar_intensity=0.3,
        fed_meeting_proximity_days=45,
        major_events_impact=0.2
    )
    
    # Demo stock factors for AAPL
    demo_stock_factors = StockSpecificFactors(
        symbol="AAPL",
        iv_rank=0.75,
        iv_percentile=0.78,
        technical_confluence_score=0.85,
        rsi=58.5,
        macd_signal=0.65,
        bollinger_position=0.7,
        volume_profile_score=0.8,
        support_resistance_score=0.75,
        earnings_days_away=35,
        earnings_surprise_history=0.6,
        analyst_sentiment_score=0.8,
        news_sentiment_score=0.7,
        options_volume=15000,
        options_open_interest=45000,
        bid_ask_spread=0.03,
        gamma_exposure=0.4,
        delta_hedging_flow=0.2,
        sector_performance=0.65,
        sector_rotation_impact="positive",
        wheel_suitability_score=0.9,
        covered_call_attractiveness=0.85,
        credit_spread_opportunity=0.8,
        leaps_opportunity=0.7
    )
    
    # Demo strategy recommendation
    demo_strategy_rec = StrategyRecommendation(
        symbol="AAPL",
        primary_strategy=StrategyType.COVERED_CALL,
        confidence=0.85,
        confidence_level=StrategyConfidence.HIGH,
        market_environment_score=0.8,
        stock_specific_score=0.85,
        erica_criteria_score=0.9,
        risk_adjusted_score=0.75,
        key_supporting_factors=[
            "High IV rank (75th percentile)",
            "Strong technical confluence",
            "Optimal earnings timing",
            "Excellent liquidity"
        ],
        key_risk_factors=[
            "Potential upside breakout",
            "Earnings volatility risk"
        ],
        alternative_strategies=[
            (StrategyType.CREDIT_SPREAD, 0.75),
            (StrategyType.PREMIUM_SELLING, 0.70)
        ],
        recommended_dte=(30, 45),
        recommended_delta=(0.25, 0.35),
        position_size_multiplier=1.0,
        invalidation_triggers=[
            "VIX drops below 15",
            "Technical breakdown below support",
            "Earnings announcement moved up"
        ],
        upgrade_triggers=[
            "IV rank increases above 80%",
            "Technical breakout confirmation"
        ]
    )
    
    return demo_market_factors, demo_stock_factors, demo_strategy_rec

def demo_criteria_analysis():
    """Demonstrate the criteria analysis functionality"""
    try:
        from strategy_criteria_display import StrategyCriteriaAnalyzer
        
        # Create demo data
        market_factors, stock_factors, strategy_rec = create_demo_data()
        
        # Analyze criteria
        analyzer = StrategyCriteriaAnalyzer()
        analysis = analyzer.analyze_strategy_criteria(strategy_rec, market_factors, stock_factors)
        
        print("=" * 60)
        print("STRATEGY CRITERIA ANALYSIS DEMO")
        print("=" * 60)
        print(f"Symbol: {analysis.symbol}")
        print(f"Strategy: {analysis.strategy.value.replace('_', ' ').title()}")
        print(f"Overall Score: {analysis.overall_score:.0%}")
        print(f"Recommendation Strength: {analysis.recommendation_strength}")
        print()
        print("Summary:")
        print(analysis.summary)
        print()
        
        # Show criteria breakdown
        categories = [
            ("Market Criteria", analysis.market_criteria),
            ("Stock Criteria", analysis.stock_criteria),
            ("Erica's Criteria", analysis.erica_criteria),
            ("Risk Criteria", analysis.risk_criteria)
        ]
        
        for category_name, criteria_list in categories:
            if criteria_list:
                print(f"{category_name}:")
                print("-" * 20)
                for criteria in criteria_list:
                    status_symbol = "✓" if criteria.status.value == "met" else "✗" if criteria.status.value == "not_met" else "⚠"
                    print(f"{status_symbol} {criteria.name}: {criteria.description} (Score: {criteria.score:.0%})")
                print()
        
        return True
        
    except Exception as e:
        print(f"Error in criteria analysis demo: {e}")
        return False

def launch_enhanced_desktop():
    """Launch the enhanced desktop application"""
    try:
        from desktop_app import TradingSystemGUI
        
        print("Launching Enhanced Desktop Application...")
        print("New features include:")
        print("- Strategy Criteria Analysis tab")
        print("- Visual criteria checklist with color-coded indicators")
        print("- Real-time criteria evaluation")
        print("- Detailed strategy justification")
        print("- Export functionality for criteria analysis")
        print()
        
        app = TradingSystemGUI()
        app.run()
        
    except Exception as e:
        print(f"Error launching desktop application: {e}")
        messagebox.showerror("Launch Error", f"Failed to launch application: {e}")

def main():
    """Main demo function"""
    print("Enhanced Trading System Desktop Application Demo")
    print("=" * 50)
    
    # Check dependencies
    if not check_dependencies():
        print("Cannot proceed without required modules.")
        return
    
    print("All required modules found.")
    print()
    
    # Run criteria analysis demo
    print("Running criteria analysis demo...")
    if demo_criteria_analysis():
        print("Criteria analysis demo completed successfully.")
    else:
        print("Criteria analysis demo failed.")
    
    print()
    
    # Ask user if they want to launch the GUI
    try:
        response = input("Launch the enhanced desktop application? (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            launch_enhanced_desktop()
        else:
            print("Demo completed. Thank you!")
    except KeyboardInterrupt:
        print("\nDemo interrupted. Goodbye!")

if __name__ == "__main__":
    main()
